services:
  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "redis-cli ping | grep PONG"]
      interval: 5s
      timeout: 3s
      retries: 5
    command: ["redis-server"]

  redisinsight:
    image: redis/redisinsight:latest
    container_name: redisinsight
    ports:
      - "8001:5540"
    restart: unless-stopped
    depends_on:
      redis:
        condition: service_healthy

volumes:
  redis-data:
