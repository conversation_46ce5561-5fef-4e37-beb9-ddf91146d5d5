
body {
    margin: 0;
    padding: 0;
    font-family: sans-serif;
    font-size: 12pt;
}

body, a, a:visited {
    color: #303030;
}

#content {
    padding: 30px 50px;
}

#content h1 {
    font-size: 160%;
    margin-bottom: 10px;
}

#footer {
    margin-top: 100px;
    font-size: 80%;
    white-space: nowrap;
}

#footer, #footer a {
    color: #a0a0a0;
}

#line-wrapping-toggle {
    vertical-align: middle;
}

#label-for-line-wrapping-toggle {
    vertical-align: middle;
}

ul {
    margin-left: 0;
}

h1, h2, h3 {
    white-space: nowrap;
}

h2 {
    font-size: 120%;
}

.tab-container .tab-container {
    margin-left: 8px;
}

ul.tabLinks {
    padding: 0;
    margin-bottom: 0;
    overflow: auto;
    min-width: 800px;
    width: auto;
    border-bottom: solid 1px #aaa;
}

ul.tabLinks li {
    float: left;
    height: 100%;
    list-style: none;
    padding: 5px 10px;
    border-radius: 7px 7px 0 0;
    border: solid 1px transparent;
    border-bottom: none;
    margin-right: 6px;
    background-color: #f0f0f0;
}

ul.tabLinks li.deselected > a {
    color: #6d6d6d;
}

ul.tabLinks li:hover {
    background-color: #fafafa;
}

ul.tabLinks li.selected {
    background-color: #c5f0f5;
    border-color: #aaa;
}

ul.tabLinks a {
    font-size: 120%;
    display: block;
    outline: none;
    text-decoration: none;
    margin: 0;
    padding: 0;
}

ul.tabLinks li h2 {
    margin: 0;
    padding: 0;
}

div.tab {
}

div.selected {
    display: block;
}

div.deselected {
    display: none;
}

div.tab table {
    min-width: 350px;
    width: auto;
    border-collapse: collapse;
}

div.tab th, div.tab table {
    border-bottom: solid 1px #d0d0d0;
}

div.tab th {
    text-align: left;
    white-space: nowrap;
    padding-left: 6em;
}

div.tab th:first-child {
    padding-left: 0;
}

div.tab td {
    white-space: nowrap;
    padding-left: 6em;
    padding-top: 5px;
    padding-bottom: 5px;
}

div.tab td:first-child {
    padding-left: 0;
}

div.tab td.numeric, div.tab th.numeric {
    text-align: right;
}

span.code {
    display: inline-block;
    margin-top: 0;
    margin-bottom: 1em;
}

span.code pre {
    font-size: 11pt;
    padding: 10px;
    margin: 0;
    background-color: #f7f7f7;
    border: solid 1px #d0d0d0;
    min-width: 700px;
    width: auto;
}

span.wrapped pre {
    word-wrap: break-word;
    white-space: pre-wrap;
    word-break: break-all;
}

label.hidden {
    display: none;
}
