<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Package org.learn.cache.redis.redis_cache</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Package org.learn.cache.redis.redis_cache</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; org.learn.cache.redis.redis_cache</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">1</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">1</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">0.002s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox failures" id="successRate">
<div class="percent">0%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div class="tab-container">
<ul class="tabLinks">
<li>
<a href="#">Failed tests</a>
</li>
<li>
<a href="#">Classes</a>
</li>
</ul>
<div class="tab">
<h2>Failed tests</h2>
<ul class="linkList">
<li>
<a href="../classes/org.learn.cache.redis.redis_cache.RedisCacheApplicationTests.html">RedisCacheApplicationTests</a>.
<a href="../classes/org.learn.cache.redis.redis_cache.RedisCacheApplicationTests.html#initializationError">initializationError</a>
</li>
</ul>
</div>
<div class="tab">
<h2>Classes</h2>
<table>
<thead>
<tr>
<th>Class</th>
<th>Tests</th>
<th>Failures</th>
<th>Ignored</th>
<th>Duration</th>
<th>Success rate</th>
</tr>
</thead>
<tr>
<td class="failures">
<a href="../classes/org.learn.cache.redis.redis_cache.RedisCacheApplicationTests.html">RedisCacheApplicationTests</a>
</td>
<td>1</td>
<td>1</td>
<td>0</td>
<td>0.002s</td>
<td class="failures">0%</td>
</tr>
</table>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="https://www.gradle.org">Gradle 8.14.2</a> at Jun 20, 2025, 7:48:12 AM</p>
</div>
</div>
</body>
</html>
