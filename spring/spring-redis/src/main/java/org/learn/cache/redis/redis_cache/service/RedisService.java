package org.learn.cache.redis.redis_cache.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
@Slf4j
public class RedisService {

    private final RedisTemplate<String, Object> redisTemplate;

    // Basic String operations
    public void setValue(String key, Object value) {
        log.info("Setting key: {} with value: {}", key, value);
        redisTemplate.opsForValue().set(key, value);
    }

    public void setValue(String key, Object value, long timeout, TimeUnit unit) {
        log.info("Setting key: {} with value: {} and timeout: {} {}", key, value, timeout, unit);
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    public void setValue(String key, Object value, Duration timeout) {
        log.info("Setting key: {} with value: {} and timeout: {}", key, value, timeout);
        redisTemplate.opsForValue().set(key, value, timeout);
    }

    public Object getValue(String key) {
        log.info("Getting value for key: {}", key);
        return redisTemplate.opsForValue().get(key);
    }

    public Boolean deleteKey(String key) {
        log.info("Deleting key: {}", key);
        return redisTemplate.delete(key);
    }

    public Boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    public Boolean expire(String key, long timeout, TimeUnit unit) {
        log.info("Setting expiration for key: {} to {} {}", key, timeout, unit);
        return redisTemplate.expire(key, timeout, unit);
    }

    public Long getExpire(String key) {
        return redisTemplate.getExpire(key);
    }

    // Hash operations
    public void setHashValue(String key, String hashKey, Object value) {
        log.info("Setting hash key: {} field: {} with value: {}", key, hashKey, value);
        redisTemplate.opsForHash().put(key, hashKey, value);
    }

    public Object getHashValue(String key, String hashKey) {
        log.info("Getting hash value for key: {} field: {}", key, hashKey);
        return redisTemplate.opsForHash().get(key, hashKey);
    }

    public Boolean deleteHashKey(String key, String hashKey) {
        log.info("Deleting hash key: {} field: {}", key, hashKey);
        return redisTemplate.opsForHash().delete(key, hashKey) > 0;
    }

    // List operations
    public Long pushToList(String key, Object value) {
        log.info("Pushing to list key: {} value: {}", key, value);
        return redisTemplate.opsForList().rightPush(key, value);
    }

    public Object popFromList(String key) {
        log.info("Popping from list key: {}", key);
        return redisTemplate.opsForList().rightPop(key);
    }

    public Long getListSize(String key) {
        return redisTemplate.opsForList().size(key);
    }

    // Set operations
    public Long addToSet(String key, Object... values) {
        log.info("Adding to set key: {} values: {}", key, values);
        return redisTemplate.opsForSet().add(key, values);
    }

    public Set<Object> getSetMembers(String key) {
        log.info("Getting set members for key: {}", key);
        return redisTemplate.opsForSet().members(key);
    }

    public Boolean isSetMember(String key, Object value) {
        return redisTemplate.opsForSet().isMember(key, value);
    }

    // Utility methods
    public Set<String> getAllKeys(String pattern) {
        log.info("Getting all keys matching pattern: {}", pattern);
        return redisTemplate.keys(pattern);
    }

    public void flushAll() {
        log.warn("Flushing all Redis data");
        redisTemplate.getConnectionFactory().getConnection().flushAll();
    }
}
