package org.learn.cache.redis.redis_cache.controller;

import lombok.RequiredArgsConstructor;
import org.learn.cache.redis.redis_cache.model.User;
import org.learn.cache.redis.redis_cache.service.RedisService;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping("/api/redis")
@RequiredArgsConstructor
public class RedisController {

    private final RedisService redisService;

    // String operations
    @PostMapping("/string/{key}")
    public ResponseEntity<String> setValue(@PathVariable String key, @RequestBody String value) {
        redisService.setValue(key, value);
        return ResponseEntity.ok("Value set successfully");
    }

    @PostMapping("/string/{key}/ttl/{seconds}")
    public ResponseEntity<String> setValueWithTTL(@PathVariable String key, 
                                                  @PathVariable long seconds,
                                                  @RequestBody String value) {
        redisService.setValue(key, value, seconds, TimeUnit.SECONDS);
        return ResponseEntity.ok("Value set with TTL successfully");
    }

    @GetMapping("/string/{key}")
    public ResponseEntity<Object> getValue(@PathVariable String key) {
        Object value = redisService.getValue(key);
        if (value == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(value);
    }

    @DeleteMapping("/string/{key}")
    public ResponseEntity<String> deleteValue(@PathVariable String key) {
        Boolean deleted = redisService.deleteKey(key);
        return ResponseEntity.ok(deleted ? "Key deleted successfully" : "Key not found");
    }

    // User operations (JSON serialization example)
    @PostMapping("/user/{id}")
    public ResponseEntity<String> saveUser(@PathVariable String id, @RequestBody User user) {
        user.setId(id);
        redisService.setValue("user:" + id, user);
        return ResponseEntity.ok("User saved successfully");
    }

    @PostMapping("/user/{id}/ttl/{minutes}")
    public ResponseEntity<String> saveUserWithTTL(@PathVariable String id, 
                                                  @PathVariable long minutes,
                                                  @RequestBody User user) {
        user.setId(id);
        redisService.setValue("user:" + id, user, Duration.ofMinutes(minutes));
        return ResponseEntity.ok("User saved with TTL successfully");
    }

    @GetMapping("/user/{id}")
    public ResponseEntity<User> getUser(@PathVariable String id) {
        Object value = redisService.getValue("user:" + id);
        if (value == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok((User) value);
    }

    // Hash operations
    @PostMapping("/hash/{key}/{field}")
    public ResponseEntity<String> setHashValue(@PathVariable String key, 
                                               @PathVariable String field,
                                               @RequestBody String value) {
        redisService.setHashValue(key, field, value);
        return ResponseEntity.ok("Hash value set successfully");
    }

    @GetMapping("/hash/{key}/{field}")
    public ResponseEntity<Object> getHashValue(@PathVariable String key, @PathVariable String field) {
        Object value = redisService.getHashValue(key, field);
        if (value == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(value);
    }

    // List operations
    @PostMapping("/list/{key}")
    public ResponseEntity<String> pushToList(@PathVariable String key, @RequestBody String value) {
        Long size = redisService.pushToList(key, value);
        return ResponseEntity.ok("Value pushed to list. New size: " + size);
    }

    @GetMapping("/list/{key}/pop")
    public ResponseEntity<Object> popFromList(@PathVariable String key) {
        Object value = redisService.popFromList(key);
        if (value == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(value);
    }

    @GetMapping("/list/{key}/size")
    public ResponseEntity<Long> getListSize(@PathVariable String key) {
        Long size = redisService.getListSize(key);
        return ResponseEntity.ok(size);
    }

    // Set operations
    @PostMapping("/set/{key}")
    public ResponseEntity<String> addToSet(@PathVariable String key, @RequestBody String value) {
        Long added = redisService.addToSet(key, value);
        return ResponseEntity.ok("Added to set. Elements added: " + added);
    }

    @GetMapping("/set/{key}")
    public ResponseEntity<Set<Object>> getSetMembers(@PathVariable String key) {
        Set<Object> members = redisService.getSetMembers(key);
        return ResponseEntity.ok(members);
    }

    // Utility operations
    @GetMapping("/keys")
    public ResponseEntity<Set<String>> getAllKeys(@RequestParam(defaultValue = "*") String pattern) {
        Set<String> keys = redisService.getAllKeys(pattern);
        return ResponseEntity.ok(keys);
    }

    @GetMapping("/exists/{key}")
    public ResponseEntity<Map<String, Object>> keyExists(@PathVariable String key) {
        Boolean exists = redisService.hasKey(key);
        Long ttl = exists ? redisService.getExpire(key) : null;
        
        Map<String, Object> response = new HashMap<>();
        response.put("exists", exists);
        response.put("ttl", ttl);
        
        return ResponseEntity.ok(response);
    }

    @PostMapping("/expire/{key}/{seconds}")
    public ResponseEntity<String> setExpiration(@PathVariable String key, @PathVariable long seconds) {
        Boolean result = redisService.expire(key, seconds, TimeUnit.SECONDS);
        return ResponseEntity.ok(result ? "Expiration set successfully" : "Key not found");
    }

    @DeleteMapping("/flush")
    public ResponseEntity<String> flushAll() {
        redisService.flushAll();
        return ResponseEntity.ok("All data flushed successfully");
    }
}
